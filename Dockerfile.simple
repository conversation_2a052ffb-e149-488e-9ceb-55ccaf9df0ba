# 简化版生产Dockerfile - 仅后端
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install uv

# 复制项目配置文件
COPY backend/pyproject.toml backend/uv.lock ./

# 安装Python依赖
RUN uv sync --frozen

# 复制后端代码
COPY backend/ ./backend/
COPY engines/ ./engines/

# 设置Python路径
ENV PATH="/app/.venv/bin:$PATH"

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/utils/health-check || exit 1

# 启动命令
CMD ["uvicorn", "backend.app.main:app", "--host", "0.0.0.0", "--port", "8000"]
