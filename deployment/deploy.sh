#!/bin/bash
set -e

echo "🚀 开始部署 Master-Know..."

# 检查Docker和docker-compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

# 构建前端
echo "🎨 构建前端..."
cd demo/frontend_poc
npm install
npm run build
cd ../..

# 停止旧服务
echo "🛑 停止旧服务..."
docker-compose -f docker-compose.prod.yml down

# 构建并启动新服务
echo "🔧 构建并启动服务..."
docker-compose -f docker-compose.prod.yml up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🏥 健康检查..."
if curl -f http://localhost:8000/api/v1/utils/health-check; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常"
    docker-compose -f docker-compose.prod.yml logs backend
    exit 1
fi

if curl -f http://localhost/; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
    docker-compose -f docker-compose.prod.yml logs frontend
    exit 1
fi

echo "🎉 部署完成！"
echo "🌐 前端: http://localhost"
echo "🔗 API: http://localhost:8000"
