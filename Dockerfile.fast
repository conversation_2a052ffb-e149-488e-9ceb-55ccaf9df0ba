# 使用国内镜像源的Python基础镜像
FROM python:3.12-slim

# 设置构建参数
ARG PYTHON_MIRROR=https://pypi.tuna.tsinghua.edu.cn/simple
ARG APT_MIRROR=mirrors.tuna.tsinghua.edu.cn

# 配置APT使用国内镜像源
RUN sed -i "s/deb.debian.org/${APT_MIRROR}/g" /etc/apt/sources.list.d/debian.sources && \
    sed -i "s/security.debian.org/${APT_MIRROR}/g" /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 配置pip使用国内镜像源
RUN pip config set global.index-url ${PYTHON_MIRROR} && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 升级pip
RUN pip install --upgrade pip

# 复制依赖文件
COPY backend/pyproject.toml backend/uv.lock* ./

# 安装uv包管理器
RUN pip install uv

# 使用uv安装依赖
RUN uv pip install --system -r pyproject.toml

# 复制应用代码
COPY backend/ ./
COPY engines/ ./engines/
COPY shared/ ./shared/

# 设置Python路径
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["fastapi", "run", "app/main.py", "--host", "0.0.0.0", "--port", "8000"]
