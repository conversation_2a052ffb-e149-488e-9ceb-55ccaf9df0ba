#!/bin/bash

# Docker容器测试脚本
echo "🚀 测试Docker容器构建和运行"

# 停止可能存在的容器
docker stop master-know-test 2>/dev/null || true

# 运行容器（带基本环境变量）
echo "📦 启动容器..."
docker run --rm -d \
  -p 8001:8000 \
  --name master-know-test \
  -e PROJECT_NAME="Master Know Test" \
  -e POSTGRES_SERVER="localhost" \
  -e POSTGRES_USER="test" \
  -e POSTGRES_PASSWORD="test" \
  -e POSTGRES_DB="test" \
  -e FIRST_SUPERUSER="<EMAIL>" \
  -e FIRST_SUPERUSER_PASSWORD="testpass" \
  -e SECRET_KEY="test-secret-key-for-docker-testing-only" \
  master-know-uv-fast:latest

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 5

# 检查容器状态
echo "🔍 检查容器状态..."
docker ps | grep master-know-test

# 检查容器日志
echo "📋 容器日志："
docker logs master-know-test

# 测试API端点
echo "🌐 测试API端点..."
curl -s http://localhost:8001/docs > /dev/null && echo "✅ API文档可访问" || echo "❌ API文档不可访问"

# 清理
echo "🧹 清理容器..."
docker stop master-know-test

echo "✨ 测试完成！"
