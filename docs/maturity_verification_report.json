{"maturity": {"overall_score": 95.0, "category_scores": {"目录结构": 100.0, "核心文件": 100.0, "后端服务": 100.0, "数据模型": 100.0, "前端POC": 100.0, "测试覆盖": 100.0, "文档完整性": 100.0, "导入测试": 0.0}, "status_counts": {"pass": 47, "fail": 3}, "total_checks": 50, "timestamp": 1755252274.299371}, "detailed_results": [{"category": "目录结构", "item": "后端应用目录", "status": "pass", "score": 100, "details": "文件存在: backend/app", "timestamp": 1755252274.298733}, {"category": "目录结构", "item": "API路由目录", "status": "pass", "score": 100, "details": "文件存在: backend/app/api", "timestamp": 1755252274.298748}, {"category": "目录结构", "item": "数据模型目录", "status": "pass", "score": 100, "details": "文件存在: backend/app/models", "timestamp": 1755252274.298757}, {"category": "目录结构", "item": "CRUD操作目录", "status": "pass", "score": 100, "details": "文件存在: backend/app/crud", "timestamp": 1755252274.2987661}, {"category": "目录结构", "item": "业务服务目录", "status": "pass", "score": 100, "details": "文件存在: backend/app/services", "timestamp": 1755252274.298774}, {"category": "目录结构", "item": "前端目录", "status": "pass", "score": 100, "details": "文件存在: frontend", "timestamp": 1755252274.2987819}, {"category": "目录结构", "item": "文本分割引擎", "status": "pass", "score": 100, "details": "文件存在: engines/text_splitter", "timestamp": 1755252274.298791}, {"category": "目录结构", "item": "项目文档目录", "status": "pass", "score": 100, "details": "文件存在: docs", "timestamp": 1755252274.298805}, {"category": "目录结构", "item": "前端POC目录", "status": "pass", "score": 100, "details": "文件存在: demo/frontend_poc", "timestamp": 1755252274.298816}, {"category": "核心文件", "item": "FastAPI主应用", "status": "pass", "score": 100, "details": "文件存在: backend/app/main.py", "timestamp": 1755252274.298829}, {"category": "核心文件", "item": "API路由主文件", "status": "pass", "score": 100, "details": "文件存在: backend/app/api/main.py", "timestamp": 1755252274.298839}, {"category": "核心文件", "item": "模型导出文件", "status": "pass", "score": 100, "details": "文件存在: backend/app/models/__init__.py", "timestamp": 1755252274.298848}, {"category": "核心文件", "item": "CRUD导出文件", "status": "pass", "score": 100, "details": "文件存在: backend/app/crud/__init__.py", "timestamp": 1755252274.298857}, {"category": "核心文件", "item": "Docker编排文件", "status": "pass", "score": 100, "details": "文件存在: docker-compose.yml", "timestamp": 1755252274.2988648}, {"category": "核心文件", "item": "项目蓝图", "status": "pass", "score": 100, "details": "文件存在: docs/PROJECT_BLUEPRINT.md", "timestamp": 1755252274.2988732}, {"category": "核心文件", "item": "API文档", "status": "pass", "score": 100, "details": "文件存在: docs/API.md", "timestamp": 1755252274.298881}, {"category": "核心文件", "item": "项目说明", "status": "pass", "score": 100, "details": "文件存在: README.md", "timestamp": 1755252274.298889}, {"category": "后端服务", "item": "用户服务", "status": "pass", "score": 100, "details": "文件存在: backend/app/api/routes/users.py", "timestamp": 1755252274.298901}, {"category": "后端服务", "item": "文档服务", "status": "pass", "score": 100, "details": "文件存在: backend/app/api/routes/documents.py", "timestamp": 1755252274.298909}, {"category": "后端服务", "item": "主题服务", "status": "pass", "score": 100, "details": "文件存在: backend/app/api/routes/topics.py", "timestamp": 1755252274.298916}, {"category": "后端服务", "item": "对话服务", "status": "pass", "score": 100, "details": "文件存在: backend/app/api/routes/conversations.py", "timestamp": 1755252274.2989252}, {"category": "后端服务", "item": "搜索服务", "status": "pass", "score": 100, "details": "文件存在: backend/app/api/routes/search.py", "timestamp": 1755252274.298932}, {"category": "后端服务", "item": "向量化服务", "status": "pass", "score": 100, "details": "文件存在: backend/app/api/routes/embedding.py", "timestamp": 1755252274.298939}, {"category": "后端服务", "item": "LLM集成服务", "status": "pass", "score": 100, "details": "文件存在: backend/app/api/routes/llm.py", "timestamp": 1755252274.2989461}, {"category": "数据模型", "item": "用户模型", "status": "pass", "score": 100, "details": "文件存在: backend/app/models/user.py", "timestamp": 1755252274.298957}, {"category": "数据模型", "item": "文档模型", "status": "pass", "score": 100, "details": "文件存在: backend/app/models/document.py", "timestamp": 1755252274.298965}, {"category": "数据模型", "item": "主题模型", "status": "pass", "score": 100, "details": "文件存在: backend/app/models/topic.py", "timestamp": 1755252274.2989728}, {"category": "数据模型", "item": "对话模型", "status": "pass", "score": 100, "details": "文件存在: backend/app/models/conversation.py", "timestamp": 1755252274.298979}, {"category": "前端POC", "item": "前端依赖配置", "status": "pass", "score": 100, "details": "文件存在: demo/frontend_poc/package.json", "timestamp": 1755252274.29899}, {"category": "前端POC", "item": "主应用组件", "status": "pass", "score": 100, "details": "文件存在: demo/frontend_poc/src/App.tsx", "timestamp": 1755252274.298999}, {"category": "前端POC", "item": "API客户端目录", "status": "pass", "score": 100, "details": "文件存在: demo/frontend_poc/src/api", "timestamp": 1755252274.299008}, {"category": "前端POC", "item": "组件目录", "status": "pass", "score": 100, "details": "文件存在: demo/frontend_poc/src/components", "timestamp": 1755252274.299017}, {"category": "前端POC", "item": "路由目录", "status": "pass", "score": 100, "details": "文件存在: demo/frontend_poc/src/routes", "timestamp": 1755252274.299026}, {"category": "前端POC", "item": "前端文档", "status": "pass", "score": 100, "details": "文件存在: demo/frontend_poc/README.md", "timestamp": 1755252274.299034}, {"category": "测试覆盖", "item": "系统集成测试", "status": "pass", "score": 100, "details": "文件存在: backend/system_integration_test.py", "timestamp": 1755252274.2990441}, {"category": "测试覆盖", "item": "完整功能测试", "status": "pass", "score": 100, "details": "文件存在: backend/test_all_features.py", "timestamp": 1755252274.299053}, {"category": "测试覆盖", "item": "简化系统测试", "status": "pass", "score": 100, "details": "文件存在: backend/simplified_system_test.py", "timestamp": 1755252274.2990599}, {"category": "测试覆盖", "item": "单元测试目录", "status": "pass", "score": 100, "details": "文件存在: backend/app/tests", "timestamp": 1755252274.299068}, {"category": "测试覆盖", "item": "前端集成测试", "status": "pass", "score": 100, "details": "文件存在: demo/frontend_poc/integration_test.py", "timestamp": 1755252274.2990751}, {"category": "测试覆盖", "item": "端到端测试", "status": "pass", "score": 100, "details": "文件存在: test_system_integration.py", "timestamp": 1755252274.299083}, {"category": "文档完整性", "item": "产品简介", "status": "pass", "score": 100, "details": "文件存在: docs/1_Product/01_brief.md", "timestamp": 1755252274.299093}, {"category": "文档完整性", "item": "项目蓝图", "status": "pass", "score": 100, "details": "文件存在: docs/PROJECT_BLUEPRINT.md", "timestamp": 1755252274.2991}, {"category": "文档完整性", "item": "架构文档", "status": "pass", "score": 100, "details": "文件存在: docs/ARCHITECTURE.md", "timestamp": 1755252274.299107}, {"category": "文档完整性", "item": "API文档", "status": "pass", "score": 100, "details": "文件存在: docs/API.md", "timestamp": 1755252274.299113}, {"category": "文档完整性", "item": "开发指南", "status": "pass", "score": 100, "details": "文件存在: docs/DEVELOPMENT.md", "timestamp": 1755252274.299121}, {"category": "文档完整性", "item": "部署指南", "status": "pass", "score": 100, "details": "文件存在: docs/DEPLOYMENT.md", "timestamp": 1755252274.299128}, {"category": "文档完整性", "item": "主题服务完成报告", "status": "pass", "score": 100, "details": "文件存在: docs/TOPIC_SERVICE_COMPLETION_REPORT.md", "timestamp": 1755252274.299135}, {"category": "导入测试", "item": "核心模型导入", "status": "fail", "score": 0, "details": "导入失败: No module named 'backend'", "timestamp": 1755252274.299223}, {"category": "导入测试", "item": "CRUD操作导入", "status": "fail", "score": 0, "details": "导入失败: No module named 'backend'", "timestamp": 1755252274.299291}, {"category": "导入测试", "item": "API路由导入", "status": "fail", "score": 0, "details": "导入失败: No module named 'backend'", "timestamp": 1755252274.299354}]}