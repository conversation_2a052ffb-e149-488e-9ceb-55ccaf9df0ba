# 多阶段构建 - 前端
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend
COPY demo/frontend_poc/package*.json ./
RUN npm ci --only=production

COPY demo/frontend_poc/ ./
RUN npm run build

# 多阶段构建 - 后端
FROM python:3.11-slim AS backend-builder

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器
RUN pip install uv

# 复制项目配置文件
COPY backend/pyproject.toml backend/uv.lock ./

# 安装Python依赖
RUN uv sync --frozen

COPY backend/ ./backend/
COPY engines/ ./engines/

# 生产镜像
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    nginx \
    && rm -rf /var/lib/apt/lists/*

# 复制后端代码和依赖
COPY --from=backend-builder /app/.venv /app/.venv
COPY --from=backend-builder /app/backend ./backend
COPY --from=backend-builder /app/engines ./engines

# 设置Python路径
ENV PATH="/app/.venv/bin:$PATH"

# 复制前端构建产物
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist

# 复制nginx配置
COPY deployment/nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80 8000

# 启动脚本
COPY deployment/start.sh ./
RUN chmod +x start.sh

CMD ["./start.sh"]
