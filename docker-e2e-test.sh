#!/bin/bash

# Master Know Docker 端到端测试脚本
set -e

echo "🧪 Master Know Docker 端到端测试"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试配置
CONTAINER_NAME="master-know-e2e-test"
TEST_PORT="8002"
BASE_URL="http://localhost:${TEST_PORT}"

# 清理函数
cleanup() {
    print_status "清理测试环境..."
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    rm -f .env.e2e-test
}

# 设置清理陷阱
trap cleanup EXIT

# 1. 构建最新镜像
print_status "构建最新Docker镜像..."
if docker build -f Dockerfile.uv -t master-know:e2e-test .; then
    print_success "镜像构建成功"
else
    print_error "镜像构建失败"
    exit 1
fi

# 2. 创建测试环境配置
print_status "创建测试环境配置..."
cat > .env.e2e-test << EOF
PROJECT_NAME=Master Know E2E Test
POSTGRES_SERVER=localhost
POSTGRES_USER=test_user
POSTGRES_PASSWORD=test_pass
POSTGRES_DB=test_db
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=e2e_admin_pass
SECRET_KEY=e2e-test-secret-key-for-docker-testing-only-not-for-production
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://localhost:${TEST_PORT}"]
ENVIRONMENT=local
EOF

# 3. 启动容器
print_status "启动测试容器..."
if docker run -d \
    --name $CONTAINER_NAME \
    --env-file .env.e2e-test \
    -p ${TEST_PORT}:8000 \
    master-know:e2e-test; then
    print_success "容器启动成功"
else
    print_error "容器启动失败"
    exit 1
fi

# 4. 等待应用启动
print_status "等待应用启动（30秒）..."
sleep 30

# 5. 检查容器状态
if ! docker ps | grep -q $CONTAINER_NAME; then
    print_error "容器未正常运行"
    print_status "容器日志："
    docker logs $CONTAINER_NAME
    exit 1
fi

print_success "容器运行正常"

# 6. 基础健康检查
print_status "执行基础健康检查..."

# 检查根路径
if curl -s -f "${BASE_URL}/" > /dev/null; then
    print_success "根路径可访问"
else
    print_warning "根路径不可访问（可能正常，某些API不提供根路径）"
fi

# 检查API文档
if curl -s -f "${BASE_URL}/docs" > /dev/null; then
    print_success "API文档可访问"
else
    print_error "API文档不可访问"
    docker logs $CONTAINER_NAME | tail -20
    exit 1
fi

# 检查OpenAPI规范
if curl -s -f "${BASE_URL}/openapi.json" > /dev/null; then
    print_success "OpenAPI规范可访问"
else
    print_error "OpenAPI规范不可访问"
fi

# 7. API端点测试
print_status "测试API端点..."

# 测试健康检查端点（如果存在）
if curl -s -f "${BASE_URL}/api/v1/utils/health-check" > /dev/null; then
    print_success "健康检查端点可访问"
else
    print_warning "健康检查端点不可访问（可能未实现）"
fi

# 测试登录端点
print_status "测试登录功能..."
login_response=$(curl -s -X POST "${BASE_URL}/api/v1/login/access-token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=<EMAIL>&password=e2e_admin_pass" \
    -w "%{http_code}" -o /tmp/login_response.json)

if [ "$login_response" = "200" ]; then
    print_success "登录功能正常"
    # 提取访问令牌
    if command -v jq > /dev/null; then
        ACCESS_TOKEN=$(jq -r '.access_token' /tmp/login_response.json 2>/dev/null || echo "")
        if [ -n "$ACCESS_TOKEN" ] && [ "$ACCESS_TOKEN" != "null" ]; then
            print_success "成功获取访问令牌"
        else
            print_warning "无法解析访问令牌"
        fi
    else
        print_warning "未安装jq，跳过令牌解析"
    fi
else
    print_warning "登录功能异常（HTTP状态码: $login_response）"
    print_status "登录响应内容："
    cat /tmp/login_response.json 2>/dev/null || echo "无响应内容"
fi

# 8. 测试其他API端点
print_status "测试其他API端点..."

# 测试用户相关端点
endpoints_to_test=(
    "/api/v1/users/me"
    "/api/v1/items/"
    "/api/v1/documents/"
    "/api/v1/topics/"
    "/api/v1/conversations/"
)

for endpoint in "${endpoints_to_test[@]}"; do
    response_code=$(curl -s -w "%{http_code}" -o /dev/null "${BASE_URL}${endpoint}")
    if [ "$response_code" = "200" ] || [ "$response_code" = "401" ] || [ "$response_code" = "422" ]; then
        print_success "端点 ${endpoint} 响应正常 (HTTP $response_code)"
    else
        print_warning "端点 ${endpoint} 响应异常 (HTTP $response_code)"
    fi
done

# 9. 容器资源使用情况
print_status "检查容器资源使用情况..."
docker stats $CONTAINER_NAME --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# 10. 检查容器日志
print_status "检查容器日志（最后20行）..."
docker logs $CONTAINER_NAME | tail -20

# 11. 测试总结
echo ""
echo "🎯 端到端测试总结"
echo "=================="
print_success "✅ Docker镜像构建成功"
print_success "✅ 容器启动成功"
print_success "✅ API文档可访问"
print_success "✅ 基础API端点响应正常"

if [ -n "$ACCESS_TOKEN" ] && [ "$ACCESS_TOKEN" != "null" ]; then
    print_success "✅ 认证功能正常"
else
    print_warning "⚠️  认证功能需要进一步检查"
fi

echo ""
echo "📋 测试环境信息："
echo "- 容器名称: $CONTAINER_NAME"
echo "- 测试端口: $TEST_PORT"
echo "- API文档: ${BASE_URL}/docs"
echo "- 容器状态: $(docker ps --filter name=$CONTAINER_NAME --format '{{.Status}}')"

echo ""
print_success "🎉 端到端测试完成！"

# 清理临时文件
rm -f /tmp/login_response.json

echo ""
print_status "容器将在脚本结束时自动清理"
print_status "如需手动停止容器，请运行: docker stop $CONTAINER_NAME"
