name: Build and Deploy

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install Python dependencies
      run: |
        cd backend
        pip install -r requirements.txt
    
    - name: Install Node.js dependencies
      run: |
        cd demo/frontend_poc
        npm ci
    
    - name: Run Python tests
      run: |
        cd backend
        python -m pytest app/tests/ -v
    
    - name: Build frontend
      run: |
        cd demo/frontend_poc
        npm run build
    
    - name: Run integration tests
      run: |
        python3 scripts/verify_implementation_maturity.py

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: |
        docker build -f Dockerfile.prod -t master-know:latest .
    
    - name: Save Docker image
      run: |
        docker save master-know:latest | gzip > master-know.tar.gz
    
    - name: Upload artifact
      uses: actions/upload-artifact@v3
      with:
        name: docker-image
        path: master-know.tar.gz
