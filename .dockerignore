# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDEs
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.sqlite
*.db
dump.rdb
htmlcov/
.env
.env.local
.env.*.local

# Documentation
docs/
README.md
*.md

# Tests
backend/test_*.py
backend/app/tests/
scripts/verify_*.py
scripts/next_*.py
scripts/execute_*.py
test_*.py

# Keep frontend POC but exclude other demo content
demo/
!demo/frontend_poc/

# Build artifacts
dist/
build/
*.egg-info/

# Logs
logs/
*.log
