import { createFileRoute } from '@tanstack/react-router';
import { useAuth } from '@/hooks/useAuth';
import { LoginForm } from '@/components/Auth/LoginForm';
import { Dashboard } from '@/components/Dashboard/Dashboard';
import { Container, Center } from '@chakra-ui/react';

function IndexPage() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return null; // 由根路由的 pendingComponent 处理
  }

  if (!isAuthenticated) {
    return (
      <Container maxW="md" py={20}>
        <Center>
          <LoginForm onSuccess={() => window.location.reload()} />
        </Center>
      </Container>
    );
  }

  return <Dashboard />;
}

export const Route = createFileRoute('/' as any)({
  component: IndexPage,
});
