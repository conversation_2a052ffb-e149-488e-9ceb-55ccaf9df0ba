import React from 'react';
import {
  <PERSON>,
  Container,
  Heading,
  SimpleGrid,
  Card,
  VStack,
  HStack,
  Button,
  Text,
  Icon,
} from '@chakra-ui/react';
import { FiFileText, FiSearch, FiMessageCircle, FiTag } from 'react-icons/fi';
import { useAuth } from '@/hooks/useAuth';

interface DashboardCardProps {
  title: string;
  count: number;
  icon: any;
  description: string;
  onAction?: () => void;
  actionText?: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  count,
  icon,
  description,
  onAction,
  actionText = '查看详情',
}) => {
  return (
    <Card.Root bg="white" shadow="md">
      <Card.Body>
        <VStack align="stretch" gap={4}>
          <HStack justify="space-between">
            <VStack align="start" gap={1}>
              <Text fontSize="sm" color="gray.500" fontWeight="medium">
                {title}
              </Text>
              <Text fontSize="2xl" fontWeight="bold">
                {count}
              </Text>
            </VStack>
            <Icon as={icon} boxSize={8} color="blue.500" />
          </HStack>
          
          <Text fontSize="sm" color="gray.600">
            {description}
          </Text>
          
          {onAction && (
            <Button size="sm" variant="outline" onClick={onAction}>
              {actionText}
            </Button>
          )}
        </VStack>
      </Card.Body>
    </Card.Root>
  );
};

export const Dashboard: React.FC = () => {
  const { user } = useAuth();

  const handleNavigate = (path: string) => {
    // 这里可以使用路由导航
    console.log(`Navigate to: ${path}`);
  };

  return (
    <Container maxW="7xl" py={8}>
      <VStack align="stretch" gap={8}>
        {/* 欢迎信息 */}
        <Box>
          <Heading size="lg" mb={2}>
            欢迎回来，{user?.full_name || user?.email}！
          </Heading>
          <Text color="gray.600">
            这是您的 Master-Know 工作台，您可以在这里管理文档、进行智能搜索和对话。
          </Text>
        </Box>

        {/* 统计卡片 */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} gap={6}>
          <DashboardCard
            title="我的文档"
            count={12}
            icon={FiFileText}
            description="已上传和处理的文档数量"
            onAction={() => handleNavigate('/documents')}
            actionText="管理文档"
          />
          
          <DashboardCard
            title="搜索记录"
            count={45}
            icon={FiSearch}
            description="最近的搜索查询次数"
            onAction={() => handleNavigate('/search')}
            actionText="开始搜索"
          />
          
          <DashboardCard
            title="对话会话"
            count={8}
            icon={FiMessageCircle}
            description="活跃的AI对话会话"
            onAction={() => handleNavigate('/chat')}
            actionText="开始对话"
          />
          
          <DashboardCard
            title="主题分类"
            count={5}
            icon={FiTag}
            description="创建的知识主题数量"
            onAction={() => handleNavigate('/topics')}
            actionText="管理主题"
          />
        </SimpleGrid>

        {/* 快速操作 */}
        <Card.Root>
          <Card.Body>
            <VStack align="stretch" gap={4}>
              <Heading size="md">快速操作</Heading>

              <SimpleGrid columns={{ base: 1, md: 3 }} gap={4}>
                <Button
                  colorScheme="blue"
                  variant="outline"
                  onClick={() => handleNavigate('/documents/new')}
                >
                  <Icon as={FiFileText} mr={2} />
                  上传新文档
                </Button>

                <Button
                  colorScheme="green"
                  variant="outline"
                  onClick={() => handleNavigate('/search')}
                >
                  <Icon as={FiSearch} mr={2} />
                  智能搜索
                </Button>

                <Button
                  colorScheme="purple"
                  variant="outline"
                  onClick={() => handleNavigate('/chat')}
                >
                  <Icon as={FiMessageCircle} mr={2} />
                  AI对话
                </Button>
              </SimpleGrid>
            </VStack>
          </Card.Body>
        </Card.Root>

        {/* 最近活动 */}
        <Card.Root>
          <Card.Body>
            <VStack align="stretch" gap={4}>
              <Heading size="md">最近活动</Heading>

              <VStack align="stretch" gap={3}>
                <HStack justify="space-between" p={3} bg="gray.50" borderRadius="md">
                  <VStack align="start" gap={1}>
                    <Text fontWeight="medium">上传了文档 "深度学习基础"</Text>
                    <Text fontSize="sm" color="gray.500">2小时前</Text>
                  </VStack>
                  <Button size="sm" variant="ghost">查看</Button>
                </HStack>

                <HStack justify="space-between" p={3} bg="gray.50" borderRadius="md">
                  <VStack align="start" gap={1}>
                    <Text fontWeight="medium">创建了新的对话会话</Text>
                    <Text fontSize="sm" color="gray.500">5小时前</Text>
                  </VStack>
                  <Button size="sm" variant="ghost">查看</Button>
                </HStack>

                <HStack justify="space-between" p={3} bg="gray.50" borderRadius="md">
                  <VStack align="start" gap={1}>
                    <Text fontWeight="medium">搜索了 "机器学习算法"</Text>
                    <Text fontSize="sm" color="gray.500">1天前</Text>
                  </VStack>
                  <Button size="sm" variant="ghost">查看</Button>
                </HStack>
              </VStack>
            </VStack>
          </Card.Body>
        </Card.Root>
      </VStack>
    </Container>
  );
};
