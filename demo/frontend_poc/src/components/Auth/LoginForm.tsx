import React, { useState } from 'react';
import {
  Box,
  Button,
  Input,
  VStack,
  Heading,
  Text,
  Card,
  Stack,
} from '@chakra-ui/react';
import { Toaster } from '@chakra-ui/react/toast';
import { useAuth } from '@/hooks/useAuth';

interface LoginFormProps {
  onSuccess?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { login } = useAuth();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    // 清除错误信息
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.username || !formData.password) {
      setError('请填写用户名和密码');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await login({
        username: formData.username,
        password: formData.password,
      });

      if (result.success) {
        // 简单的成功提示，可以后续改为toast
        console.log('登录成功');
        onSuccess?.();
      } else {
        setError(result.error || '登录失败');
      }
    } catch (error: any) {
      setError(error.message || '登录失败');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card.Root maxW="md" mx="auto">
      <Card.Body>
        <VStack gap={6}>
          <Heading size="lg" textAlign="center">
            登录 Master-Know
          </Heading>
          
          <Text color="gray.600" textAlign="center">
            请输入您的账号信息
          </Text>

          {error && (
            <Box p={3} bg="red.50" borderRadius="md" color="red.600">
              {error}
            </Box>
          )}

          <Box as="form" onSubmit={handleSubmit} w="100%">
            <VStack gap={4}>
              <Stack gap={2}>
                <Text fontSize="sm" fontWeight="medium">用户名/邮箱</Text>
                <Input
                  name="username"
                  type="email"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="请输入邮箱地址"
                  disabled={isSubmitting}
                  required
                />
              </Stack>

              <Stack gap={2}>
                <Text fontSize="sm" fontWeight="medium">密码</Text>
                <Input
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="请输入密码"
                  disabled={isSubmitting}
                  required
                />
              </Stack>

              <Button
                type="submit"
                colorScheme="blue"
                size="lg"
                w="100%"
                loading={isSubmitting}
              >
                {isSubmitting ? '登录中...' : '登录'}
              </Button>
            </VStack>
          </Box>

          <Text fontSize="sm" color="gray.500" textAlign="center">
            默认管理员账号: <EMAIL> / changethis
          </Text>
        </VStack>
      </Card.Body>
    </Card.Root>
  );
};

// 导出Toaster组件供App使用
export { Toaster };
