import React from 'react';
import { ChakraProvider, Box, Spinner, Center, Text, Flex, createSystem, defaultConfig } from '@chakra-ui/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { RouterProvider, createRouter } from '@tanstack/react-router';
import { useAuth } from '@/hooks/useAuth';
import { APP_CONFIG } from '@/utils/constants';

// 创建路由配置
import { routeTree } from './routeTree.gen';

const router = createRouter({ routeTree });

// 声明路由器类型
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}



// 创建Chakra UI系统
const system = createSystem(defaultConfig);

// 创建React Query客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5分钟
    },
  },
});

// 认证包装组件
const AuthWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isLoading, error } = useAuth();

  if (isLoading) {
    return (
      <Center h="100vh">
        <Flex direction="column" align="center" gap={4}>
          <Spinner size="xl" color="blue.500" />
          <Text>正在加载...</Text>
        </Flex>
      </Center>
    );
  }

  if (error) {
    return (
      <Center h="100vh">
        <Flex direction="column" align="center" gap={4}>
          <Text color="red.500" fontSize="lg">
            {error}
          </Text>
          <Text color="gray.500">
            请刷新页面重试
          </Text>
        </Flex>
      </Center>
    );
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  return (
    <ChakraProvider value={system}>
      <QueryClientProvider client={queryClient}>
        <AuthWrapper>
          <Box minH="100vh" bg="gray.50">
            <RouterProvider router={router} />
          </Box>
        </AuthWrapper>
        {APP_CONFIG.DEV_MODE && <ReactQueryDevtools initialIsOpen={false} />}
      </QueryClientProvider>
    </ChakraProvider>
  );
};

export default App;
