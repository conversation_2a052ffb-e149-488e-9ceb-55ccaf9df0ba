#!/usr/bin/env python3
"""
前端POC最终验证测试

验证前端应用是否正常启动并可以访问
"""

import requests
import time
import sys

def test_frontend_accessibility():
    """测试前端应用可访问性"""
    print("🌐 测试前端应用可访问性...")
    
    frontend_url = "http://localhost:5174"
    max_retries = 10
    retry_delay = 2
    
    for attempt in range(max_retries):
        try:
            response = requests.get(frontend_url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ 前端应用可访问: {frontend_url}")
                print(f"   📄 页面标题: {extract_title(response.text)}")
                return True
            else:
                print(f"   ⚠️  前端应用响应异常: {response.status_code}")
        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1:
                print(f"   ⏳ 等待前端启动... (尝试 {attempt + 1}/{max_retries})")
                time.sleep(retry_delay)
            else:
                print(f"   ❌ 前端应用连接失败: {e}")
                return False
    
    return False

def extract_title(html_content):
    """从HTML中提取标题"""
    try:
        start = html_content.find('<title>') + 7
        end = html_content.find('</title>')
        if start > 6 and end > start:
            return html_content[start:end]
        return "未找到标题"
    except:
        return "解析失败"

def test_api_endpoints():
    """测试API端点可访问性"""
    print("\n🔗 测试API端点可访问性...")
    
    api_endpoints = [
        ("健康检查", "http://api.localhost.tiangolo.com/api/v1/utils/health-check/"),
        ("API文档", "http://api.localhost.tiangolo.com/docs"),
    ]
    
    all_passed = True
    for name, url in api_endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {name}: {url}")
            else:
                print(f"   ⚠️  {name} 响应异常: {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"   ❌ {name} 连接失败: {e}")
            all_passed = False
    
    return all_passed

def print_success_summary():
    """打印成功总结"""
    print("\n" + "=" * 70)
    print("🎉 前端POC验证完成！")
    print("=" * 70)
    print()
    print("✅ 所有测试通过，系统已准备就绪！")
    print()
    print("🌐 访问地址:")
    print("   前端应用: http://localhost:5174/")
    print("   API文档:  http://api.localhost.tiangolo.com/docs")
    print()
    print("🔐 默认登录信息:")
    print("   用户名: <EMAIL>")
    print("   密码:   changethis")
    print()
    print("📚 相关文档:")
    print("   - README.md: 项目说明")
    print("   - QUICK_START.md: 快速启动指南")
    print("   - EXPLORATION_INSIGHTS.md: 探索经验总结")
    print("   - CODE_PITFALLS.md: 代码陷阱和解决方案")
    print()
    print("🚀 下一步:")
    print("   1. 在浏览器中访问前端应用")
    print("   2. 使用默认账号登录")
    print("   3. 测试各项功能")
    print("   4. 基于此POC继续开发")
    print()
    print("=" * 70)

def print_failure_summary():
    """打印失败总结"""
    print("\n" + "=" * 70)
    print("⚠️  前端POC验证未完全通过")
    print("=" * 70)
    print()
    print("🔧 故障排除建议:")
    print("   1. 确保前端开发服务器正在运行:")
    print("      npm run dev")
    print()
    print("   2. 确保后端服务正在运行:")
    print("      docker compose up -d")
    print()
    print("   3. 检查端口是否被占用:")
    print("      lsof -i :5174")
    print("      lsof -i :8000")
    print()
    print("   4. 查看详细错误日志")
    print()
    print("📚 参考文档:")
    print("   - QUICK_START.md: 快速启动指南")
    print("   - CODE_PITFALLS.md: 常见问题解决方案")
    print()
    print("=" * 70)

def main():
    """主函数"""
    print("🧪 Master-Know 前端POC 最终验证")
    print("=" * 50)
    
    # 测试前端可访问性
    frontend_ok = test_frontend_accessibility()
    
    # 测试API端点
    api_ok = test_api_endpoints()
    
    # 输出结果
    if frontend_ok and api_ok:
        print_success_summary()
        sys.exit(0)
    else:
        print_failure_summary()
        sys.exit(1)

if __name__ == "__main__":
    main()
