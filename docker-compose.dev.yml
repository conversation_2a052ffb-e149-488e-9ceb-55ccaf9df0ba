version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: master-know-postgres-dev
    environment:
      POSTGRES_USER: master_know_user
      POSTGRES_PASSWORD: master_know_pass
      POSTGRES_DB: master_know_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U master_know_user -d master_know_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: master-know-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 数据库初始化服务
  db-init:
    build:
      context: .
      dockerfile: Dockerfile.uv
    container_name: master-know-db-init-dev
    environment:
      # 项目配置
      PROJECT_NAME: "Master Know Dev"
      ENVIRONMENT: "local"

      # 数据库配置
      POSTGRES_SERVER: postgres
      POSTGRES_PORT: 5432
      POSTGRES_USER: master_know_user
      POSTGRES_PASSWORD: master_know_pass
      POSTGRES_DB: master_know_db

      # 认证配置
      SECRET_KEY: "dev-secret-key-not-for-production"
      FIRST_SUPERUSER: "<EMAIL>"
      FIRST_SUPERUSER_PASSWORD: "dev123456"

    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: bash -c "cd backend && python app/backend_pre_start.py && alembic upgrade head && python app/initial_data.py"
    restart: "no"

  # Master Know应用
  app:
    build:
      context: .
      dockerfile: Dockerfile.uv
    container_name: master-know-app-dev
    environment:
      # 项目配置
      PROJECT_NAME: "Master Know Dev"
      ENVIRONMENT: "local"

      # 数据库配置
      POSTGRES_SERVER: postgres
      POSTGRES_PORT: 5432
      POSTGRES_USER: master_know_user
      POSTGRES_PASSWORD: master_know_pass
      POSTGRES_DB: master_know_db

      # Redis配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0

      # 认证配置
      SECRET_KEY: "dev-secret-key-not-for-production"
      FIRST_SUPERUSER: "<EMAIL>"
      FIRST_SUPERUSER_PASSWORD: "dev123456"

      # CORS配置
      BACKEND_CORS_ORIGINS: '["http://localhost:3000","http://localhost:8000"]'

    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      db-init:
        condition: service_completed_successfully
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  default:
    name: master-know-dev-network
