# 使用uv加速Python依赖安装的优化版Dockerfile
FROM python:3.11-slim AS builder

# 设置工作目录
WORKDIR /app

# 配置国内加速源
RUN echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ trixie main contrib non-free non-free-firmware" > /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ trixie-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security trixie-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list

# 安装系统依赖和uv
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && pip install -i https://pypi.tuna.tsinghua.edu.cn/simple uv

# 使用uv安装Python依赖（比pip快10-100倍，配置清华源）
RUN uv pip install --system -i https://pypi.tuna.tsinghua.edu.cn/simple \
    fastapi[standard]>=0.104.1 \
    uvicorn[standard]>=0.24.0 \
    sqlalchemy>=2.0.23 \
    psycopg[binary]>=3.1.13 \
    redis>=5.0.1 \
    dramatiq[redis]>=1.15.0 \
    pydantic>=2.5.0 \
    email-validator>=2.1.0 \
    python-multipart>=0.0.6 \
    python-jose[cryptography]>=3.3.0 \
    passlib[bcrypt]>=1.7.4 \
    aiofiles>=23.2.1 \
    httpx>=0.25.2 \
    openai>=1.3.7 \
    numpy>=1.24.0 \
    semantic-text-splitter>=0.8.2 \
    lxml>=4.9.3 \
    sentry-sdk[fastapi]>=1.40.0 \
    sqlmodel>=0.0.22 \
    alembic>=1.13.0 \
    python-dotenv>=1.0.0 \
    pydantic-settings>=2.5.0 \
    bcrypt==4.0.1 \
    pyjwt>=2.8.0 \
    manticoresearch>=3.3.1 \
    emails>=0.6 \
    jinja2>=3.1.4 \
    tenacity>=8.2.3

# 复制后端代码
COPY backend/ ./backend/

# 复制engines目录
COPY engines/ ./engines/

# 设置环境变量
ENV PYTHONPATH=/app/backend

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "backend.app.main:app", "--host", "0.0.0.0", "--port", "8000"]
