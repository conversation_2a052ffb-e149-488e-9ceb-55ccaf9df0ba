#!/usr/bin/env python3
"""
Master-Know 项目实现成熟度验证脚本

自动化检查项目各个维度的完成度和质量
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass, asdict


@dataclass
class VerificationResult:
    """验证结果数据类"""
    category: str
    item: str
    status: str  # "pass", "fail", "warning", "skip"
    score: float  # 0-100
    details: str
    timestamp: float


class MaturityVerifier:
    """实现成熟度验证器"""
    
    def __init__(self):
        self.results: List[VerificationResult] = []
        self.project_root = Path(__file__).parent.parent
        
    def log_result(self, category: str, item: str, status: str, score: float, details: str):
        """记录验证结果"""
        result = VerificationResult(
            category=category,
            item=item,
            status=status,
            score=score,
            details=details,
            timestamp=time.time()
        )
        self.results.append(result)
        
        # 实时输出
        status_icon = {"pass": "✅", "fail": "❌", "warning": "⚠️", "skip": "⏭️"}
        print(f"{status_icon.get(status, '?')} [{category}] {item}: {details}")
    
    def check_file_exists(self, file_path: str, category: str, description: str) -> bool:
        """检查文件是否存在"""
        full_path = self.project_root / file_path
        exists = full_path.exists()
        
        if exists:
            self.log_result(category, description, "pass", 100, f"文件存在: {file_path}")
        else:
            self.log_result(category, description, "fail", 0, f"文件缺失: {file_path}")
        
        return exists
    
    def check_directory_structure(self) -> float:
        """检查目录结构完整性"""
        print("\n🏗️ 检查项目目录结构...")
        
        required_dirs = [
            ("backend/app", "后端应用目录"),
            ("backend/app/api", "API路由目录"),
            ("backend/app/models", "数据模型目录"),
            ("backend/app/crud", "CRUD操作目录"),
            ("backend/app/services", "业务服务目录"),
            ("frontend", "前端目录"),
            ("engines/text_splitter", "文本分割引擎"),
            ("docs", "项目文档目录"),
            ("demo/frontend_poc", "前端POC目录"),
        ]
        
        passed = 0
        for dir_path, description in required_dirs:
            if self.check_file_exists(dir_path, "目录结构", description):
                passed += 1
        
        score = (passed / len(required_dirs)) * 100
        return score
    
    def check_core_files(self) -> float:
        """检查核心文件完整性"""
        print("\n📄 检查核心文件...")
        
        core_files = [
            ("backend/app/main.py", "FastAPI主应用"),
            ("backend/app/api/main.py", "API路由主文件"),
            ("backend/app/models/__init__.py", "模型导出文件"),
            ("backend/app/crud/__init__.py", "CRUD导出文件"),
            ("docker-compose.yml", "Docker编排文件"),
            ("docs/PROJECT_BLUEPRINT.md", "项目蓝图"),
            ("docs/API.md", "API文档"),
            ("README.md", "项目说明"),
        ]
        
        passed = 0
        for file_path, description in core_files:
            if self.check_file_exists(file_path, "核心文件", description):
                passed += 1
        
        score = (passed / len(core_files)) * 100
        return score
    
    def check_backend_services(self) -> float:
        """检查后端服务实现"""
        print("\n🔧 检查后端服务实现...")
        
        services = [
            ("backend/app/api/routes/users.py", "用户服务"),
            ("backend/app/api/routes/documents.py", "文档服务"),
            ("backend/app/api/routes/topics.py", "主题服务"),
            ("backend/app/api/routes/conversations.py", "对话服务"),
            ("backend/app/api/routes/search.py", "搜索服务"),
            ("backend/app/api/routes/embedding.py", "向量化服务"),
            ("backend/app/api/routes/llm.py", "LLM集成服务"),
        ]
        
        passed = 0
        for file_path, description in services:
            if self.check_file_exists(file_path, "后端服务", description):
                passed += 1
        
        score = (passed / len(services)) * 100
        return score
    
    def check_data_models(self) -> float:
        """检查数据模型完整性"""
        print("\n📊 检查数据模型...")
        
        models = [
            ("backend/app/models/user.py", "用户模型"),
            ("backend/app/models/document.py", "文档模型"),
            ("backend/app/models/topic.py", "主题模型"),
            ("backend/app/models/conversation.py", "对话模型"),
        ]
        
        passed = 0
        for file_path, description in models:
            if self.check_file_exists(file_path, "数据模型", description):
                passed += 1
        
        score = (passed / len(models)) * 100
        return score
    
    def check_frontend_poc(self) -> float:
        """检查前端POC实现"""
        print("\n🎨 检查前端POC...")
        
        frontend_files = [
            ("demo/frontend_poc/package.json", "前端依赖配置"),
            ("demo/frontend_poc/src/App.tsx", "主应用组件"),
            ("demo/frontend_poc/src/api", "API客户端目录"),
            ("demo/frontend_poc/src/components", "组件目录"),
            ("demo/frontend_poc/src/routes", "路由目录"),
            ("demo/frontend_poc/README.md", "前端文档"),
        ]
        
        passed = 0
        for file_path, description in frontend_files:
            if self.check_file_exists(file_path, "前端POC", description):
                passed += 1
        
        score = (passed / len(frontend_files)) * 100
        return score
    
    def check_test_coverage(self) -> float:
        """检查测试覆盖情况"""
        print("\n🧪 检查测试覆盖...")
        
        test_files = [
            ("backend/system_integration_test.py", "系统集成测试"),
            ("backend/test_all_features.py", "完整功能测试"),
            ("backend/simplified_system_test.py", "简化系统测试"),
            ("backend/app/tests", "单元测试目录"),
            ("demo/frontend_poc/integration_test.py", "前端集成测试"),
            ("test_system_integration.py", "端到端测试"),
        ]
        
        passed = 0
        for file_path, description in test_files:
            if self.check_file_exists(file_path, "测试覆盖", description):
                passed += 1
        
        score = (passed / len(test_files)) * 100
        return score
    
    def check_documentation(self) -> float:
        """检查文档完整性"""
        print("\n📚 检查文档完整性...")
        
        docs = [
            ("docs/1_Product/01_brief.md", "产品简介"),
            ("docs/PROJECT_BLUEPRINT.md", "项目蓝图"),
            ("docs/ARCHITECTURE.md", "架构文档"),
            ("docs/API.md", "API文档"),
            ("docs/DEVELOPMENT.md", "开发指南"),
            ("docs/DEPLOYMENT.md", "部署指南"),
            ("docs/TOPIC_SERVICE_COMPLETION_REPORT.md", "主题服务完成报告"),
        ]
        
        passed = 0
        for file_path, description in docs:
            if self.check_file_exists(file_path, "文档完整性", description):
                passed += 1
        
        score = (passed / len(docs)) * 100
        return score
    
    def run_import_tests(self) -> float:
        """运行导入测试"""
        print("\n🔍 运行导入测试...")
        
        import_tests = [
            ("from backend.app.models import Document, Topic, Conversation", "核心模型导入"),
            ("from backend.app.crud import document, topic, conversation", "CRUD操作导入"),
            ("from backend.app.api.routes import documents, topics", "API路由导入"),
        ]
        
        passed = 0
        for import_stmt, description in import_tests:
            try:
                # 切换到项目根目录
                original_cwd = os.getcwd()
                os.chdir(self.project_root)
                
                # 尝试导入
                exec(import_stmt)
                self.log_result("导入测试", description, "pass", 100, "导入成功")
                passed += 1
                
            except Exception as e:
                self.log_result("导入测试", description, "fail", 0, f"导入失败: {str(e)}")
            finally:
                os.chdir(original_cwd)
        
        score = (passed / len(import_tests)) * 100
        return score
    
    def calculate_overall_maturity(self) -> Dict[str, Any]:
        """计算总体成熟度"""
        print("\n📊 计算总体成熟度...")
        
        # 运行所有检查
        scores = {
            "目录结构": self.check_directory_structure(),
            "核心文件": self.check_core_files(),
            "后端服务": self.check_backend_services(),
            "数据模型": self.check_data_models(),
            "前端POC": self.check_frontend_poc(),
            "测试覆盖": self.check_test_coverage(),
            "文档完整性": self.check_documentation(),
            "导入测试": self.run_import_tests(),
        }
        
        # 计算加权平均分
        weights = {
            "目录结构": 0.10,
            "核心文件": 0.15,
            "后端服务": 0.20,
            "数据模型": 0.15,
            "前端POC": 0.15,
            "测试覆盖": 0.10,
            "文档完整性": 0.10,
            "导入测试": 0.05,
        }
        
        weighted_score = sum(scores[category] * weights[category] for category in scores)
        
        # 统计各状态的数量
        status_counts = {}
        for result in self.results:
            status_counts[result.status] = status_counts.get(result.status, 0) + 1
        
        return {
            "overall_score": round(weighted_score, 1),
            "category_scores": scores,
            "status_counts": status_counts,
            "total_checks": len(self.results),
            "timestamp": time.time()
        }
    
    def generate_report(self) -> str:
        """生成验证报告"""
        maturity = self.calculate_overall_maturity()
        
        print("\n" + "="*80)
        print("🎯 Master-Know 项目实现成熟度验证报告")
        print("="*80)
        
        print(f"\n📊 总体成熟度: {maturity['overall_score']}%")
        
        # 成熟度等级
        score = maturity['overall_score']
        if score >= 90:
            level = "🟢 优秀 (生产就绪)"
        elif score >= 80:
            level = "🟢 良好 (MVP就绪)"
        elif score >= 70:
            level = "🟡 可用 (需要完善)"
        elif score >= 60:
            level = "🟡 基础 (需要改进)"
        else:
            level = "🔴 不足 (需要重构)"
        
        print(f"📈 成熟度等级: {level}")
        
        print(f"\n📋 各维度得分:")
        for category, score in maturity['category_scores'].items():
            print(f"   {category}: {score:.1f}%")
        
        print(f"\n📊 检查统计:")
        for status, count in maturity['status_counts'].items():
            icon = {"pass": "✅", "fail": "❌", "warning": "⚠️", "skip": "⏭️"}.get(status, "?")
            print(f"   {icon} {status}: {count}")
        
        print(f"\n总检查项: {maturity['total_checks']}")
        
        # 保存详细结果到JSON
        report_data = {
            "maturity": maturity,
            "detailed_results": [asdict(result) for result in self.results]
        }
        
        report_file = self.project_root / "docs" / "maturity_verification_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return level


def main():
    """主函数"""
    print("🚀 开始 Master-Know 项目实现成熟度验证...")
    
    verifier = MaturityVerifier()
    level = verifier.generate_report()
    
    print("\n" + "="*80)
    print("✨ 验证完成!")
    print("="*80)
    
    # 根据成熟度等级返回不同的退出码
    if "优秀" in level or "良好" in level:
        sys.exit(0)  # 成功
    elif "可用" in level or "基础" in level:
        sys.exit(1)  # 警告
    else:
        sys.exit(2)  # 失败


if __name__ == "__main__":
    main()
