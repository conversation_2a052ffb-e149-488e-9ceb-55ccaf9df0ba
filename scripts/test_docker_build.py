#!/usr/bin/env python3
"""
Docker构建测试脚本

测试生产环境Docker镜像的构建和基本功能
"""

import subprocess
import time
import sys
from pathlib import Path


class DockerBuildTester:
    """Docker构建测试器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.image_name = "master-know:latest"
        
    def run_command(self, command: str, description: str, timeout: int = 300) -> bool:
        """运行命令并返回结果"""
        print(f"\n🔧 {description}")
        print(f"💻 命令: {command}")
        
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            if result.returncode == 0:
                print(f"✅ 成功: {description}")
                return True
            else:
                print(f"❌ 失败: {description}")
                print(f"📤 错误: {result.stderr.strip()}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 超时: {description}")
            return False
        except Exception as e:
            print(f"💥 异常: {description} - {str(e)}")
            return False
    
    def test_docker_build(self) -> bool:
        """测试Docker构建"""
        print("🐳 测试Docker镜像构建...")
        
        # 清理旧镜像
        self.run_command(
            f"docker rmi {self.image_name} 2>/dev/null || true",
            "清理旧镜像",
            30
        )
        
        # 构建新镜像
        success = self.run_command(
            f"docker build -f Dockerfile.prod -t {self.image_name} .",
            "构建Docker镜像",
            600  # 10分钟超时
        )
        
        return success
    
    def test_docker_run(self) -> bool:
        """测试Docker运行"""
        print("🚀 测试Docker容器运行...")
        
        # 停止并删除旧容器
        self.run_command(
            "docker stop master-know-test 2>/dev/null || true",
            "停止旧容器",
            30
        )
        self.run_command(
            "docker rm master-know-test 2>/dev/null || true",
            "删除旧容器",
            30
        )
        
        # 启动新容器
        success = self.run_command(
            f"docker run -d --name master-know-test -p 8080:80 {self.image_name}",
            "启动Docker容器",
            60
        )
        
        if not success:
            return False
        
        # 等待容器启动
        print("⏳ 等待容器启动...")
        time.sleep(10)
        
        # 检查容器状态
        result = subprocess.run(
            "docker ps --filter name=master-know-test --format '{{.Status}}'",
            shell=True,
            capture_output=True,
            text=True
        )
        
        if "Up" in result.stdout:
            print("✅ 容器启动成功")
            return True
        else:
            print("❌ 容器启动失败")
            # 显示容器日志
            self.run_command(
                "docker logs master-know-test",
                "查看容器日志",
                30
            )
            return False
    
    def test_health_check(self) -> bool:
        """测试健康检查"""
        print("🏥 测试应用健康检查...")
        
        # 等待应用启动
        print("⏳ 等待应用启动...")
        time.sleep(5)
        
        # 测试前端访问
        frontend_success = self.run_command(
            "curl -f http://localhost:8080/ -o /dev/null -s",
            "测试前端访问",
            30
        )
        
        # 测试API访问
        api_success = self.run_command(
            "curl -f http://localhost:8080/api/v1/utils/health-check -o /dev/null -s",
            "测试API健康检查",
            30
        )
        
        return frontend_success and api_success
    
    def cleanup(self):
        """清理测试资源"""
        print("🧹 清理测试资源...")
        
        self.run_command(
            "docker stop master-know-test 2>/dev/null || true",
            "停止测试容器",
            30
        )
        self.run_command(
            "docker rm master-know-test 2>/dev/null || true",
            "删除测试容器",
            30
        )
    
    def run_tests(self) -> bool:
        """运行所有测试"""
        print("🚀 开始Docker构建测试...")
        print("="*80)
        
        tests = [
            ("Docker镜像构建", self.test_docker_build),
            ("Docker容器运行", self.test_docker_run),
            ("应用健康检查", self.test_health_check),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            print(f"\n{'-'*60}")
            print(f"🧪 执行测试: {test_name}")
            
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"💥 测试异常: {test_name} - {str(e)}")
                results[test_name] = False
        
        # 清理资源
        self.cleanup()
        
        # 生成报告
        self.generate_report(results)
        
        # 返回总体结果
        return all(results.values())
    
    def generate_report(self, results: dict):
        """生成测试报告"""
        print("\n" + "="*80)
        print("📋 Docker构建测试报告")
        print("="*80)
        
        total_tests = len(results)
        passed_tests = sum(1 for success in results.values() if success)
        
        print(f"\n📊 测试概览:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n📋 详细结果:")
        for test_name, success in results.items():
            status = "✅ 通过" if success else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        if passed_tests == total_tests:
            print(f"\n🎉 所有测试通过！Docker镜像构建成功！")
            print(f"📋 下一步:")
            print(f"   1. 推送镜像到仓库: docker push {self.image_name}")
            print(f"   2. 部署到生产环境")
            print(f"   3. 配置域名和SSL证书")
        else:
            print(f"\n⚠️ 部分测试失败，请检查错误信息并修复问题")


def main():
    """主函数"""
    tester = DockerBuildTester()
    
    try:
        success = tester.run_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        tester.cleanup()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行异常: {str(e)}")
        tester.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    main()
