#!/usr/bin/env python3
"""
部署优化计划执行脚本

解决Docker构建问题，优化部署流程
"""

import os
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Any


class DeploymentOptimizer:
    """部署优化器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "demo" / "frontend_poc"
        
    def run_command(self, command: str, description: str, cwd: Path = None) -> bool:
        """运行命令并返回结果"""
        if cwd is None:
            cwd = self.project_root
            
        print(f"\n🔧 {description}")
        print(f"💻 {command}")
        
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                print(f"✅ 成功: {description}")
                return True
            else:
                print(f"❌ 失败: {description}")
                print(f"错误: {result.stderr.strip()}")
                return False
                
        except Exception as e:
            print(f"💥 异常: {description} - {str(e)}")
            return False
    
    def fix_backend_pyproject(self) -> bool:
        """修复后端pyproject.toml配置"""
        print("\n📝 修复后端pyproject.toml配置...")
        
        pyproject_path = self.backend_dir / "pyproject.toml"
        
        if not pyproject_path.exists():
            print("❌ pyproject.toml 不存在")
            return False
        
        # 读取现有配置
        with open(pyproject_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加hatch构建配置
        if "[tool.hatch.build.targets.wheel]" not in content:
            build_config = '''

[tool.hatch.build.targets.wheel]
packages = ["app"]
'''
            content += build_config
            
            with open(pyproject_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 已添加hatch构建配置")
            return True
        else:
            print("✅ hatch构建配置已存在")
            return True
    
    def create_requirements_txt(self) -> bool:
        """从pyproject.toml生成requirements.txt"""
        print("\n📦 生成requirements.txt...")
        
        # 使用uv导出依赖
        success = self.run_command(
            "uv export --format requirements-txt --output-file requirements.txt",
            "导出依赖到requirements.txt",
            self.backend_dir
        )
        
        if success:
            print("✅ requirements.txt 生成成功")
            return True
        else:
            # 手动创建基础requirements.txt
            print("⚠️ 自动导出失败，创建基础requirements.txt")
            
            basic_requirements = """fastapi>=0.104.1
uvicorn[standard]>=0.24.0
sqlalchemy>=2.0.23
psycopg[binary]>=3.1.13
redis>=5.0.1
dramatiq[redis]>=1.15.0
pydantic>=2.5.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
aiofiles>=23.2.1
httpx>=0.25.2
openai>=1.3.7
numpy>=1.24.0
semantic-text-splitter>=0.8.2
lxml>=4.9.3
"""
            
            requirements_path = self.backend_dir / "requirements.txt"
            with open(requirements_path, 'w') as f:
                f.write(basic_requirements)
            
            print("✅ 基础requirements.txt 创建成功")
            return True
    
    def create_optimized_dockerfile(self) -> bool:
        """创建优化的Dockerfile"""
        print("\n🐳 创建优化的Dockerfile...")
        
        dockerfile_content = '''# 优化版生产Dockerfile
FROM python:3.11-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    git \\
    curl \\
    build-essential \\
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY backend/requirements.txt ./requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# 复制后端代码
COPY backend/ ./backend/
COPY engines/ ./engines/

# 设置Python路径
ENV PYTHONPATH="/app/backend:/app/engines:$PYTHONPATH"

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:8000/api/v1/utils/health-check || exit 1

# 启动命令
CMD ["uvicorn", "backend.app.main:app", "--host", "0.0.0.0", "--port", "8000"]
'''
        
        dockerfile_path = self.project_root / "Dockerfile.optimized"
        with open(dockerfile_path, 'w') as f:
            f.write(dockerfile_content)
        
        print(f"✅ 优化Dockerfile已创建: {dockerfile_path}")
        return True
    
    def create_docker_compose_production(self) -> bool:
        """创建生产环境docker-compose"""
        print("\n🐳 创建生产环境docker-compose...")
        
        compose_content = '''version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.optimized
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/master_know
      - REDIS_URL=redis://redis:6379/0
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9308
    depends_on:
      - postgres
      - redis
      - manticore
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/utils/health-check"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 (nginx)
  frontend:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./demo/frontend_poc/dist:/usr/share/nginx/html
      - ./deployment/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
    restart: unless-stopped

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=master_know
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Manticore搜索引擎
  manticore:
    image: manticoresearch/manticore:latest
    ports:
      - "9306:9306"
      - "9308:9308"
    volumes:
      - manticore_data:/var/lib/manticore
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  manticore_data:
'''
        
        compose_path = self.project_root / "docker-compose.prod.yml"
        with open(compose_path, 'w') as f:
            f.write(compose_content)
        
        print(f"✅ 生产docker-compose已创建: {compose_path}")
        return True
    
    def create_deployment_scripts(self) -> bool:
        """创建部署脚本"""
        print("\n📜 创建部署脚本...")
        
        deployment_dir = self.project_root / "deployment"
        deployment_dir.mkdir(exist_ok=True)
        
        # 部署脚本
        deploy_script = '''#!/bin/bash
set -e

echo "🚀 开始部署 Master-Know..."

# 检查Docker和docker-compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

# 构建前端
echo "🎨 构建前端..."
cd demo/frontend_poc
npm install
npm run build
cd ../..

# 停止旧服务
echo "🛑 停止旧服务..."
docker-compose -f docker-compose.prod.yml down

# 构建并启动新服务
echo "🔧 构建并启动服务..."
docker-compose -f docker-compose.prod.yml up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🏥 健康检查..."
if curl -f http://localhost:8000/api/v1/utils/health-check; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常"
    docker-compose -f docker-compose.prod.yml logs backend
    exit 1
fi

if curl -f http://localhost/; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
    docker-compose -f docker-compose.prod.yml logs frontend
    exit 1
fi

echo "🎉 部署完成！"
echo "🌐 前端: http://localhost"
echo "🔗 API: http://localhost:8000"
'''
        
        deploy_path = deployment_dir / "deploy.sh"
        with open(deploy_path, 'w') as f:
            f.write(deploy_script)
        
        # 设置执行权限
        os.chmod(deploy_path, 0o755)
        
        # 更新脚本
        update_script = '''#!/bin/bash
set -e

echo "🔄 更新 Master-Know..."

# 拉取最新代码
git pull

# 重新构建前端
echo "🎨 重新构建前端..."
cd demo/frontend_poc
npm install
npm run build
cd ../..

# 重新构建并重启服务
echo "🔧 重新构建服务..."
docker-compose -f docker-compose.prod.yml up --build -d

echo "✅ 更新完成！"
'''
        
        update_path = deployment_dir / "update.sh"
        with open(update_path, 'w') as f:
            f.write(update_script)
        
        os.chmod(update_path, 0o755)
        
        print(f"✅ 部署脚本已创建: {deployment_dir}")
        return True
    
    def test_optimized_build(self) -> bool:
        """测试优化后的Docker构建"""
        print("\n🧪 测试优化后的Docker构建...")
        
        # 清理旧镜像
        self.run_command(
            "docker rmi master-know-optimized:latest 2>/dev/null || true",
            "清理旧镜像"
        )
        
        # 构建新镜像
        success = self.run_command(
            "docker build -f Dockerfile.optimized -t master-know-optimized:latest .",
            "构建优化Docker镜像"
        )
        
        return success
    
    def run_optimization(self) -> Dict[str, bool]:
        """运行部署优化"""
        print("🚀 开始部署优化...")
        print("="*80)
        
        results = {}
        
        # 1. 修复后端配置
        results["fix_pyproject"] = self.fix_backend_pyproject()
        
        # 2. 生成requirements.txt
        results["create_requirements"] = self.create_requirements_txt()
        
        # 3. 创建优化Dockerfile
        results["create_dockerfile"] = self.create_optimized_dockerfile()
        
        # 4. 创建生产docker-compose
        results["create_compose"] = self.create_docker_compose_production()
        
        # 5. 创建部署脚本
        results["create_scripts"] = self.create_deployment_scripts()
        
        # 6. 测试Docker构建
        results["test_build"] = self.test_optimized_build()
        
        return results
    
    def generate_report(self, results: Dict[str, bool]):
        """生成优化报告"""
        print("\n" + "="*80)
        print("📋 部署优化报告")
        print("="*80)
        
        total_tasks = len(results)
        completed_tasks = sum(1 for success in results.values() if success)
        
        print(f"\n📊 优化概览:")
        print(f"   总任务数: {total_tasks}")
        print(f"   完成任务: {completed_tasks}")
        print(f"   成功率: {(completed_tasks/total_tasks)*100:.1f}%")
        
        print(f"\n📋 详细结果:")
        task_names = {
            "fix_pyproject": "修复pyproject.toml配置",
            "create_requirements": "生成requirements.txt",
            "create_dockerfile": "创建优化Dockerfile",
            "create_compose": "创建生产docker-compose",
            "create_scripts": "创建部署脚本",
            "test_build": "测试Docker构建"
        }
        
        for task_id, success in results.items():
            task_name = task_names.get(task_id, task_id)
            status = "✅ 完成" if success else "❌ 失败"
            print(f"   {task_name}: {status}")
        
        if completed_tasks == total_tasks:
            print(f"\n🎉 部署优化全部完成！")
            print(f"📋 下一步:")
            print(f"   1. 运行部署: ./deployment/deploy.sh")
            print(f"   2. 访问应用: http://localhost")
            print(f"   3. 监控日志: docker-compose -f docker-compose.prod.yml logs -f")
        else:
            print(f"\n⚠️ 部分任务未完成，请检查错误信息")


def main():
    """主函数"""
    optimizer = DeploymentOptimizer()
    results = optimizer.run_optimization()
    optimizer.generate_report(results)


if __name__ == "__main__":
    main()
