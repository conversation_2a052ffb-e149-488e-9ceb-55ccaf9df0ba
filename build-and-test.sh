#!/bin/bash

# Master Know Docker构建和测试脚本
set -e

echo "🚀 Master Know Docker 构建和测试"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理函数
cleanup() {
    print_status "清理测试容器..."
    docker stop master-know-test 2>/dev/null || true
    docker rm master-know-test 2>/dev/null || true
}

# 设置清理陷阱
trap cleanup EXIT

# 1. 构建镜像
print_status "开始构建Docker镜像..."
start_time=$(date +%s)

if docker build -f Dockerfile.uv -t master-know:latest .; then
    end_time=$(date +%s)
    build_time=$((end_time - start_time))
    print_success "镜像构建成功！耗时: ${build_time}秒"
else
    print_error "镜像构建失败！"
    exit 1
fi

# 2. 检查镜像大小
print_status "检查镜像大小..."
image_size=$(docker images master-know:latest --format "{{.Size}}")
print_success "镜像大小: $image_size"

# 3. 创建测试环境变量文件
print_status "创建测试环境配置..."
cat > .env.test << EOF
PROJECT_NAME=Master Know Test
POSTGRES_SERVER=localhost
POSTGRES_USER=test_user
POSTGRES_PASSWORD=test_pass
POSTGRES_DB=test_db
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=test_admin_pass
SECRET_KEY=test-secret-key-for-docker-testing-only-not-for-production
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]
EOF

# 4. 测试容器启动
print_status "测试容器启动..."
if docker run -d --name master-know-test --env-file .env.test -p 8001:8000 master-know:latest; then
    print_success "容器启动成功"
else
    print_error "容器启动失败"
    exit 1
fi

# 5. 等待应用启动
print_status "等待应用启动..."
sleep 10

# 6. 检查容器状态
if docker ps | grep -q master-know-test; then
    print_success "容器运行正常"
else
    print_error "容器未正常运行"
    print_status "容器日志："
    docker logs master-know-test
    exit 1
fi

# 7. 测试API端点
print_status "测试API端点..."
if curl -s -f http://localhost:8001/docs > /dev/null; then
    print_success "API文档端点可访问"
else
    print_warning "API文档端点不可访问，检查日志..."
    docker logs master-know-test | tail -20
fi

# 8. 显示构建总结
echo ""
echo "🎉 构建和测试完成！"
echo "===================="
print_success "镜像名称: master-know:latest"
print_success "镜像大小: $image_size"
print_success "构建时间: ${build_time}秒"
print_success "测试端口: http://localhost:8001"

echo ""
echo "📋 使用说明："
echo "1. 查看API文档: http://localhost:8001/docs"
echo "2. 查看容器日志: docker logs master-know-test"
echo "3. 停止容器: docker stop master-know-test"
echo "4. 生产部署请修改环境变量配置"

# 清理测试文件
rm -f .env.test

print_success "脚本执行完成！"
