#!/bin/bash

# Master Know Docker Compose 完整端到端测试脚本
set -e

echo "🧪 Master Know Docker Compose 完整端到端测试"
echo "=============================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试配置
BASE_URL="http://localhost:8000"
COMPOSE_FILE="docker-compose.dev.yml"

# 清理函数
cleanup() {
    print_status "清理测试环境..."
    docker-compose -f $COMPOSE_FILE down -v --remove-orphans 2>/dev/null || true
}

# 设置清理陷阱
trap cleanup EXIT

# 1. 清理之前的环境
print_status "清理之前的测试环境..."
cleanup

# 2. 构建并启动所有服务
print_status "构建并启动所有服务..."
if docker-compose -f $COMPOSE_FILE up -d --build; then
    print_success "所有服务启动成功"
else
    print_error "服务启动失败"
    exit 1
fi

# 3. 等待服务启动
print_status "等待所有服务启动（60秒）..."
sleep 60

# 4. 检查服务状态
print_status "检查服务状态..."
docker-compose -f $COMPOSE_FILE ps

# 检查各个服务是否健康
services=("postgres" "redis" "app")
for service in "${services[@]}"; do
    if docker-compose -f $COMPOSE_FILE ps $service | grep -q "Up"; then
        print_success "服务 $service 运行正常"
    else
        print_error "服务 $service 未正常运行"
        docker-compose -f $COMPOSE_FILE logs $service
        exit 1
    fi
done

# 5. 基础健康检查
print_status "执行基础健康检查..."

# 检查API文档
if curl -s -f "${BASE_URL}/docs" > /dev/null; then
    print_success "API文档可访问"
else
    print_error "API文档不可访问"
    docker-compose -f $COMPOSE_FILE logs app
    exit 1
fi

# 检查健康检查端点
if curl -s -f "${BASE_URL}/api/v1/utils/health-check/" > /dev/null; then
    print_success "健康检查端点可访问"
else
    print_warning "健康检查端点不可访问"
fi

# 6. 数据库连接测试
print_status "测试数据库连接..."
login_response=$(curl -s -X POST "${BASE_URL}/api/v1/login/access-token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=<EMAIL>&password=dev123456" \
    -w "%{http_code}" -o /tmp/login_response.json)

if [ "$login_response" = "200" ]; then
    print_success "登录功能正常，数据库连接成功"
    # 提取访问令牌
    if command -v jq > /dev/null; then
        ACCESS_TOKEN=$(jq -r '.access_token' /tmp/login_response.json 2>/dev/null || echo "")
        if [ -n "$ACCESS_TOKEN" ] && [ "$ACCESS_TOKEN" != "null" ]; then
            print_success "成功获取访问令牌"
        else
            print_warning "无法解析访问令牌"
        fi
    else
        print_warning "未安装jq，跳过令牌解析"
    fi
else
    print_warning "登录功能异常（HTTP状态码: $login_response）"
    print_status "登录响应内容："
    cat /tmp/login_response.json 2>/dev/null || echo "无响应内容"
    print_status "应用日志："
    docker-compose -f $COMPOSE_FILE logs app | tail -20
fi

# 7. 认证API测试
if [ -n "$ACCESS_TOKEN" ] && [ "$ACCESS_TOKEN" != "null" ]; then
    print_status "测试认证API端点..."
    
    # 测试用户信息端点
    user_response=$(curl -s -X GET "${BASE_URL}/api/v1/users/me" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -w "%{http_code}" -o /tmp/user_response.json)
    
    if [ "$user_response" = "200" ]; then
        print_success "用户信息端点正常"
    else
        print_warning "用户信息端点异常（HTTP状态码: $user_response）"
    fi
    
    # 测试其他认证端点
    auth_endpoints=(
        "/api/v1/items/"
        "/api/v1/documents/"
        "/api/v1/topics/"
        "/api/v1/conversations/"
    )
    
    for endpoint in "${auth_endpoints[@]}"; do
        response_code=$(curl -s -w "%{http_code}" -o /dev/null \
            -H "Authorization: Bearer $ACCESS_TOKEN" \
            "${BASE_URL}${endpoint}")
        if [ "$response_code" = "200" ] || [ "$response_code" = "422" ]; then
            print_success "认证端点 ${endpoint} 响应正常 (HTTP $response_code)"
        else
            print_warning "认证端点 ${endpoint} 响应异常 (HTTP $response_code)"
        fi
    done
fi

# 8. 服务资源使用情况
print_status "检查服务资源使用情况..."
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" \
    $(docker-compose -f $COMPOSE_FILE ps -q)

# 9. 检查服务日志
print_status "检查各服务日志状态..."
for service in "${services[@]}"; do
    echo "--- $service 服务日志（最后5行）---"
    docker-compose -f $COMPOSE_FILE logs --tail=5 $service
    echo ""
done

# 10. 数据持久化测试
print_status "测试数据持久化..."
print_status "重启应用服务..."
docker-compose -f $COMPOSE_FILE restart app

print_status "等待应用重启（30秒）..."
sleep 30

# 再次测试登录
login_response2=$(curl -s -X POST "${BASE_URL}/api/v1/login/access-token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=<EMAIL>&password=dev123456" \
    -w "%{http_code}" -o /dev/null)

if [ "$login_response2" = "200" ]; then
    print_success "数据持久化正常，重启后登录仍然有效"
else
    print_warning "数据持久化可能有问题"
fi

# 11. 测试总结
echo ""
echo "🎯 Docker Compose 端到端测试总结"
echo "=================================="
print_success "✅ Docker镜像构建成功"
print_success "✅ 所有服务启动成功"
print_success "✅ API文档可访问"
print_success "✅ 基础API端点响应正常"

if [ "$login_response" = "200" ]; then
    print_success "✅ 数据库连接正常"
    print_success "✅ 认证功能正常"
else
    print_warning "⚠️  数据库连接或认证功能需要检查"
fi

if [ "$login_response2" = "200" ]; then
    print_success "✅ 数据持久化正常"
else
    print_warning "⚠️  数据持久化需要检查"
fi

echo ""
echo "📋 测试环境信息："
echo "- Compose文件: $COMPOSE_FILE"
echo "- API文档: ${BASE_URL}/docs"
echo "- 数据库: PostgreSQL (localhost:5432)"
echo "- 缓存: Redis (localhost:6379)"
echo "- 管理员账号: <EMAIL> / dev123456"

echo ""
print_success "🎉 Docker Compose 端到端测试完成！"

# 清理临时文件
rm -f /tmp/login_response.json /tmp/user_response.json

echo ""
print_status "服务将继续运行，使用以下命令管理："
print_status "停止服务: docker-compose -f $COMPOSE_FILE down"
print_status "查看日志: docker-compose -f $COMPOSE_FILE logs -f"
print_status "重启服务: docker-compose -f $COMPOSE_FILE restart"
